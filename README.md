<a href="https://www.bassetti-group.com/" ><img src="https://www.bassetti-group.com/wp-content/uploads/2019/10/logo-bassetti-quadri_250x308.png" title="Bassetti Group" alt="Bassetti Group"></a>

# Bassetti UML Web Application

> A comprehensive UML modeling platform for creating, managing, and collaborating on software architecture diagrams.

## 📚 Documentation

For comprehensive documentation, please visit our [**Documentation Portal**](./docs-portal/) built with Docusaurus v3.

## 🚀 Quick Start

### Prerequisites

-   Node.js 16+ and npm/yarn
-   .NET Core 6.0+
-   SQL Server or compatible database

### Development Setup

```bash
# Clone the repository
git clone <repository-url>
cd devint-BASSETTI-GROUP-APP

# Install and start the documentation portal
cd docs-portal
npm install
npm start

# Start the Angular applications (in separate terminals)
cd BassettiUMLWebApp/UMLApp
npm install
npm start

cd BassettiUMLWebApp/UMLAuth
npm install
npm start

# Start the .NET API
cd BassettiUMLWebApp/UMLRest/BASSUmlRest
dotnet run
```

## 🏗️ Architecture Overview

This project consists of three main components:

-   **🎨 UML Web App** (`BassettiUMLWebApp/UMLApp/`) - Angular 16 application for UML diagram creation and editing
-   **🔐 Authentication** (`BassettiUMLWebApp/UMLAuth/`) - Angular 16 authentication module
-   **🔧 REST API** (`BassettiUMLWebApp/UMLRest/`) - .NET Core backend with Entity Framework

## ✨ Key Features

-   **Interactive UML Diagrams** - Create and edit UML diagrams using GoJS
-   **Project Management** - Organize diagrams into projects with folder structures
-   **Version History** - Track changes and manage diagram versions
-   **Real-time Collaboration** - Share and collaborate on projects
-   **Multi-language Support** - Internationalization with multiple language support
-   **Authentication & Authorization** - Secure access control and user management

## 🛠️ Technologies

### Frontend

-   Angular 16 with TypeScript
-   Angular Material for UI components
-   GoJS for diagram rendering
-   RxJS for reactive programming
-   ngx-translate for internationalization

### Backend

-   .NET Core 6.0
-   Entity Framework Core
-   AutoMapper for object mapping
-   NLog for logging
-   Swagger/OpenAPI for API documentation

## 📖 Documentation Structure

-   **[Getting Started](./docs-portal/docs/getting-started/)** - Installation and setup guides
-   **[User Guide](./docs-portal/docs/user-guide/)** - How to use the application
-   **[Developer Guide](./docs-portal/docs/developer-guide/)** - Architecture and development
-   **[API Reference](./docs-portal/docs/api/)** - REST API documentation
-   **[Deployment](./docs-portal/docs/deployment/)** - Production deployment guides

## 🤝 Contributing

Please read our [Contributing Guide](./docs-portal/docs/developer-guide/contributing.md) for details on our code of conduct and the process for submitting pull requests.

## 📄 License

[![License](http://img.shields.io/:license-mit-blue.svg?style=flat-square)](http://badges.mit-license.org)

-   **[MIT license](http://opensource.org/licenses/mit-license.php)**
-   Copyright 2024 © <a href="https://www.bassetti-group.com" target="_blank">BASSETTI GROUP</a>.
